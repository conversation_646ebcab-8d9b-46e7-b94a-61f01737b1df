import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  OnDestroy,
  HostListener,
  ViewEncapsulation,
  ChangeDetectionStrategy,
  OnChanges,
  SimpleChanges,
  ElementRef,
  ViewChild,
  Renderer2,
  ChangeDetectorRef
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonComponent } from '../button/button.component';

export type DrawerPosition = 'left' | 'right' | 'top' | 'bottom';
export type DrawerSize = 'small' | 'medium' | 'large' | 'extra-large' | 'full';
export type DrawerTheme = 'light' | 'dark' | 'auto';
export type DrawerAnimation = 'slide' | 'fade' | 'scale' | 'none';
export type DrawerBackdrop = 'static' | 'blur' | 'dark' | 'light' | 'none';



@Component({
  selector: 'ava-drawer',
  standalone: true,
  imports: [CommonModule, ButtonComponent],
  templateUrl: './drawer.component.html',
  styleUrls: ['./drawer.component.scss'],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DrawerComponent implements OnInit, OnDestroy, OnChanges {

  // Core Properties
  @Input() isOpen: boolean = false;
  @Input() position: DrawerPosition = 'right';
  @Input() size: DrawerSize = 'medium';

  // Enhanced Behavior Properties
  @Input() showOverlay: boolean = true;
  @Input() closeOnOverlayClick: boolean = true;
  @Input() closeOnEscape: boolean = true;
  @Input() showCloseButton: boolean = true;
  @Input() persistent: boolean = false;
  @Input() resizable: boolean = false;
  @Input() animate: boolean = true;
  @Input() animation: DrawerAnimation = 'slide';
  @Input() backdrop: DrawerBackdrop = 'dark';
  @Input() theme: DrawerTheme = 'auto';
  @Input() autoFocus: boolean = true;
  @Input() trapFocus: boolean = true;
  @Input() restoreFocus: boolean = true;

  // Content Properties
  @Input() title: string = '';
  @Input() subtitle: string = '';
  @Input() showHeader: boolean = true;
  @Input() showFooter: boolean = false;
  @Input() headerClass: string = '';
  @Input() bodyClass: string = '';
  @Input() footerClass: string = '';

  // Enhanced Styling Properties
  @Input() width: string = '';
  @Input() height: string = '';
  @Input() maxWidth: string = '';
  @Input() maxHeight: string = '';
  @Input() minWidth: string = '';
  @Input() minHeight: string = '';
  @Input() zIndex: number = 1050;
  @Input() customClass: string = '';
  @Input() borderRadius: string = '';

  // Icon Properties
  @Input() closeIcon: string = 'X';
  @Input() closeIconSize: number = 20;

  // Enhanced Events
  @Output() opened = new EventEmitter<void>();
  @Output() closed = new EventEmitter<void>();
  @Output() overlayClick = new EventEmitter<void>();
  @Output() escapePressed = new EventEmitter<void>();
  @Output() beforeOpen = new EventEmitter<void>();
  @Output() beforeClose = new EventEmitter<void>();
  @Output() afterOpen = new EventEmitter<void>();
  @Output() afterClose = new EventEmitter<void>();
  @Output() resizeStart = new EventEmitter<{width: number, height: number}>();
  @Output() resizeEnd = new EventEmitter<{width: number, height: number}>();

  // ViewChild references
  @ViewChild('drawerElement', { static: false }) drawerElement?: ElementRef;
  @ViewChild('overlayElement', { static: false }) overlayElement?: ElementRef;

  // Internal State
  private wasOpen = false;
  private animationTimeout?: number;
  private resizeObserver?: ResizeObserver;
  private focusedElementBeforeOpen?: HTMLElement;
  private isResizing = false;
  private startX = 0;
  private startY = 0;
  private startWidth = 0;
  private startHeight = 0;

  constructor(
    private elementRef: ElementRef,
    private renderer: Renderer2,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.wasOpen = this.isOpen;
    this.setupResizeObserver();
    if (this.isOpen) {
      this.handleOpen();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['isOpen']) {
      if (this.isOpen && !this.wasOpen) {
        this.handleOpen();
      } else if (!this.isOpen && this.wasOpen) {
        this.handleClose();
      }
      this.wasOpen = this.isOpen;
    }
  }

  ngOnDestroy(): void {
    if (this.animationTimeout) {
      clearTimeout(this.animationTimeout);
    }
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
    this.removeBodyClass();
    this.restoreFocusIfNeeded();
  }

  @HostListener('document:keydown', ['$event'])
  onKeyDown(event: KeyboardEvent): void {
    if (event.key === 'Escape' && this.isOpen && this.closeOnEscape && !this.persistent) {
      this.escapePressed.emit();
      this.close();
    }
  }

  /**
   * Opens the drawer with enhanced lifecycle events
   */
  open(): void {
    if (!this.isOpen) {
      this.beforeOpen.emit();
      this.isOpen = true;
      this.handleOpen();
    }
  }

  /**
   * Closes the drawer with enhanced lifecycle events
   */
  close(): void {
    if (this.isOpen && !this.persistent) {
      this.beforeClose.emit();
      this.isOpen = false;
      this.handleClose();
    }
  }

  /**
   * Toggles the drawer open/closed state
   */
  toggle(): void {
    if (this.isOpen) {
      this.close();
    } else {
      this.open();
    }
  }

  /**
   * Forces the drawer to close even if persistent
   */
  forceClose(): void {
    if (this.isOpen) {
      this.beforeClose.emit();
      this.isOpen = false;
      this.handleClose();
    }
  }

  /**
   * Handles overlay click
   */
  onOverlayClick(): void {
    this.overlayClick.emit();
    if (this.closeOnOverlayClick && !this.persistent) {
      this.close();
    }
  }

  /**
   * Handles close button click
   */
  onCloseClick(): void {
    if (!this.persistent) {
      this.close();
    }
  }

  /**
   * Prevents event bubbling when clicking inside drawer
   */
  onDrawerClick(event: Event): void {
    event.stopPropagation();
  }

  /**
   * Gets the enhanced drawer CSS classes
   */
  getDrawerClasses(): string {
    const classes = [
      'ava-drawer',
      `ava-drawer--${this.position}`,
      `ava-drawer--${this.size}`,
      `ava-drawer--${this.animation}`,
      `ava-drawer--theme-${this.theme}`
    ];

    if (this.isOpen) {
      classes.push('ava-drawer--open');
    }

    if (this.resizable) {
      classes.push('ava-drawer--resizable');
    }

    if (this.isResizing) {
      classes.push('ava-drawer--resizing');
    }

    if (this.customClass) {
      classes.push(this.customClass);
    }

    return classes.join(' ');
  }

  /**
   * Gets the enhanced overlay CSS classes
   */
  getOverlayClasses(): string {
    const classes = [
      'ava-drawer-overlay',
      `ava-drawer-overlay--${this.backdrop}`
    ];

    if (this.isOpen) {
      classes.push('ava-drawer-overlay--open');
    }

    return classes.join(' ');
  }

  /**
   * Gets the enhanced drawer content styles
   */
  getDrawerStyles(): { [key: string]: string } {
    const styles: { [key: string]: string } = {
      'z-index': this.zIndex.toString()
    };

    if (this.width && (this.position === 'left' || this.position === 'right')) {
      styles['width'] = this.width;
    }

    if (this.height && (this.position === 'top' || this.position === 'bottom')) {
      styles['height'] = this.height;
    }

    if (this.maxWidth) {
      styles['max-width'] = this.maxWidth;
    }

    if (this.maxHeight) {
      styles['max-height'] = this.maxHeight;
    }

    if (this.minWidth) {
      styles['min-width'] = this.minWidth;
    }

    if (this.minHeight) {
      styles['min-height'] = this.minHeight;
    }

    if (this.borderRadius) {
      styles['border-radius'] = this.borderRadius;
    }

    return styles;
  }

  /**
   * Handles drawer opening
   */
  private handleOpen(): void {
    this.addBodyClass();

    // Emit opened event after animation completes
    if (this.animate) {
      this.animationTimeout = window.setTimeout(() => {
        this.opened.emit();
      }, 250); // Match CSS animation duration
    } else {
      this.opened.emit();
    }
  }

  /**
   * Handles drawer closing
   */
  private handleClose(): void {
    this.closed.emit();

    // Remove body class after animation completes
    if (this.animate) {
      this.animationTimeout = window.setTimeout(() => {
        this.removeBodyClass();
      }, 250); // Match CSS animation duration
    } else {
      this.removeBodyClass();
    }
  }

  /**
   * Adds body class to prevent scrolling
   */
  private addBodyClass(): void {
    if (this.showOverlay) {
      document.body.classList.add('ava-drawer-open');
    }
  }

  /**
   * Removes body class to restore scrolling
   */
  private removeBodyClass(): void {
    document.body.classList.remove('ava-drawer-open');
  }
}
